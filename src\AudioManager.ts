import type { AudioState } from './types';
import { getCurrentLyric } from './lyrics';

export class AudioManager {
  private audio: HTMLAudioElement;
  private state: AudioState;
  private callbacks: {
    onTimeUpdate?: (time: number) => void;
    onLyricChange?: (lyric: string, emotion: string) => void;
    onPlay?: () => void;
    onPause?: () => void;
    onEnd?: () => void;
  } = {};

  constructor() {
    this.audio = new Audio();
    this.state = {
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      volume: 0.7
    };

    this.setupEventListeners();
  }

  private setupEventListeners() {
    this.audio.addEventListener('loadedmetadata', () => {
      this.state.duration = this.audio.duration;
    });

    this.audio.addEventListener('timeupdate', () => {
      this.state.currentTime = this.audio.currentTime;
      this.callbacks.onTimeUpdate?.(this.state.currentTime);
      
      // 检查歌词变化
      const currentLyric = getCurrentLyric(this.state.currentTime);
      if (currentLyric) {
        this.callbacks.onLyricChange?.(currentLyric.text, currentLyric.emotion);
      }
    });

    this.audio.addEventListener('play', () => {
      this.state.isPlaying = true;
      this.callbacks.onPlay?.();
    });

    this.audio.addEventListener('pause', () => {
      this.state.isPlaying = false;
      this.callbacks.onPause?.();
    });

    this.audio.addEventListener('ended', () => {
      this.state.isPlaying = false;
      this.callbacks.onEnd?.();
    });
  }

  // 加载音频文件
  loadAudio(src: string) {
    this.audio.src = src;
    this.audio.volume = this.state.volume;
  }

  // 播放/暂停
  togglePlay() {
    if (this.state.isPlaying) {
      this.audio.pause();
    } else {
      this.audio.play();
    }
  }

  // 设置播放位置
  setCurrentTime(time: number) {
    this.audio.currentTime = time;
    this.state.currentTime = time;
  }

  // 设置音量
  setVolume(volume: number) {
    this.state.volume = Math.max(0, Math.min(1, volume));
    this.audio.volume = this.state.volume;
  }

  // 获取状态
  getState(): AudioState {
    return { ...this.state };
  }

  // 注册回调函数
  on(event: string, callback: Function) {
    switch (event) {
      case 'timeupdate':
        this.callbacks.onTimeUpdate = callback as (time: number) => void;
        break;
      case 'lyricchange':
        this.callbacks.onLyricChange = callback as (lyric: string, emotion: string) => void;
        break;
      case 'play':
        this.callbacks.onPlay = callback as () => void;
        break;
      case 'pause':
        this.callbacks.onPause = callback as () => void;
        break;
      case 'end':
        this.callbacks.onEnd = callback as () => void;
        break;
    }
  }

  // 格式化时间显示
  formatTime(seconds: number): string {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
}
