import './style.css';
import { AudioManager } from './AudioManager';
import { SceneManager } from './SceneManager';
import { UIManager } from './UIManager';
import { EchoWallManager } from './EchoWallManager';

class App {
  private audioManager!: AudioManager;
  private sceneManager!: SceneManager;
  private uiManager!: UIManager;
  private echoWallManager: EchoWallManager | null = null;

  constructor() {
    this.init();
  }

  private async init() {
    try {
      // 初始化音频管理器
      this.audioManager = new AudioManager();

      // 初始化3D场景
      const canvas = document.getElementById('three-canvas') as HTMLCanvasElement;
      this.sceneManager = new SceneManager(canvas);

      // 初始化UI管理器
      this.uiManager = new UIManager(this.audioManager);

      // 设置音频和场景的联动
      this.setupAudioSceneSync();

      // 启动演示模式（如果没有音频文件）
      this.startDemoMode();

      // 加载音频文件（这里需要替换为实际的音频文件路径）
      // this.uiManager.loadAudio('/be-caught-in-ones-own-trap.mp3');

      console.log('应用初始化完成');

    } catch (error) {
      console.error('应用初始化失败:', error);
    }
  }

  private setupAudioSceneSync() {
    // 监听歌词变化，更新3D场景
    this.audioManager.on('lyricchange', (lyric: string, emotion: string) => {
      console.log('歌词变化:', lyric, '情感:', emotion);

      // 根据情感类型更新场景
      switch (emotion) {
        case 'mist':
          this.sceneManager.updateEmotion('mist', 1);
          break;
        case 'cocoon':
          this.sceneManager.updateEmotion('cocoon', 1);
          break;
        case 'bubble':
          this.sceneManager.updateEmotion('bubble', 1);
          break;
        default:
          this.sceneManager.updateEmotion('normal', 0);
          break;
      }
    });

    // 监听播放结束，初始化回响墙
    this.audioManager.on('end', () => {
      this.initEchoWall();
    });
  }

  private initEchoWall() {
    if (!this.echoWallManager) {
      this.echoWallManager = new EchoWallManager();
    }
  }

  // 演示模式 - 自动播放效果演示
  private startDemoMode() {
    console.log('启动演示模式');

    // 隐藏加载界面
    setTimeout(() => {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    }, 1000);

    // 演示不同的情感效果
    const demoSequence = [
      { emotion: 'normal', duration: 2000, lyric: '欢迎来到作茧自缚的世界' },
      { emotion: 'mist', duration: 5000, lyric: '游走于谎言之中构建起来的朦胧' },
      { emotion: 'normal', duration: 2000, lyric: '' },
      { emotion: 'cocoon', duration: 5000, lyric: '是我作茧自缚' },
      { emotion: 'normal', duration: 2000, lyric: '' },
      { emotion: 'bubble', duration: 5000, lyric: '相信你 多轻浮的吻' },
      { emotion: 'normal', duration: 3000, lyric: '点击体验完整版本' }
    ];

    let currentIndex = 0;
    const runDemo = () => {
      if (currentIndex >= demoSequence.length) {
        currentIndex = 0; // 循环播放
      }

      const current = demoSequence[currentIndex];

      // 更新场景
      this.sceneManager.updateEmotion(current.emotion as any, 1);

      // 更新歌词显示
      const lyricsDisplay = document.getElementById('lyrics-display');
      if (lyricsDisplay && current.lyric) {
        lyricsDisplay.textContent = current.lyric;
        lyricsDisplay.classList.add('show');
        lyricsDisplay.style.opacity = '1';
      } else if (lyricsDisplay) {
        lyricsDisplay.style.opacity = '0';
        lyricsDisplay.classList.remove('show');
      }

      currentIndex++;
      setTimeout(runDemo, current.duration);
    };

    // 开始演示
    setTimeout(runDemo, 2000);
  }
}

// 启动应用
new App();
