import { AudioManager } from './AudioManager';
import { gsap } from 'gsap';

export class UIManager {
  private audioManager: AudioManager;
  private elements!: {
    playBtn: HTMLButtonElement;
    progressBar: HTMLElement;
    progress: HTMLElement;
    timeDisplay: HTMLElement;
    lyricsDisplay: HTMLElement;
    loading: HTMLElement;
    echoWall: HTMLElement;
    userMessage: HTMLTextAreaElement;
    submitMessage: HTMLButtonElement;
  };

  private currentLyric: string = '';
  private isTransitioning: boolean = false;

  constructor(audioManager: AudioManager) {
    this.audioManager = audioManager;
    this.initElements();
    this.setupEventListeners();
    this.setupAudioCallbacks();
  }

  private initElements() {
    this.elements = {
      playBtn: document.getElementById('play-btn') as HTMLButtonElement,
      progressBar: document.getElementById('progress-bar') as HTMLElement,
      progress: document.getElementById('progress') as HTMLElement,
      timeDisplay: document.getElementById('time-display') as HTMLElement,
      lyricsDisplay: document.getElementById('lyrics-display') as HTMLElement,
      loading: document.getElementById('loading') as HTMLElement,
      echoWall: document.getElementById('echo-wall') as HTMLElement,
      userMessage: document.getElementById('user-message') as HTMLTextAreaElement,
      submitMessage: document.getElementById('submit-message') as HTMLButtonElement
    };
  }

  private setupEventListeners() {
    // 播放按钮
    this.elements.playBtn.addEventListener('click', () => {
      this.audioManager.togglePlay();
    });

    // 进度条点击
    this.elements.progressBar.addEventListener('click', (e) => {
      const rect = this.elements.progressBar.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const width = rect.width;
      const percentage = clickX / width;
      const duration = this.audioManager.getState().duration;
      
      this.audioManager.setCurrentTime(duration * percentage);
    });

    // 留言提交
    this.elements.submitMessage.addEventListener('click', () => {
      this.submitMessage();
    });

    // 回车提交留言
    this.elements.userMessage.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.submitMessage();
      }
    });
  }

  private setupAudioCallbacks() {
    // 时间更新
    this.audioManager.on('timeupdate', (time: number) => {
      this.updateProgress(time);
      this.updateTimeDisplay(time);
    });

    // 歌词变化
    this.audioManager.on('lyricchange', (lyric: string) => {
      this.updateLyrics(lyric);
    });

    // 播放状态变化
    this.audioManager.on('play', () => {
      this.elements.playBtn.textContent = '暂停';
      this.hideLoading();
    });

    this.audioManager.on('pause', () => {
      this.elements.playBtn.textContent = '播放';
    });

    // 播放结束
    this.audioManager.on('end', () => {
      this.elements.playBtn.textContent = '播放';
      this.showEchoWall();
    });
  }

  private updateProgress(currentTime: number) {
    const state = this.audioManager.getState();
    if (state.duration > 0) {
      const percentage = (currentTime / state.duration) * 100;
      this.elements.progress.style.width = `${percentage}%`;
    }
  }

  private updateTimeDisplay(currentTime: number) {
    const state = this.audioManager.getState();
    const current = this.audioManager.formatTime(currentTime);
    const total = this.audioManager.formatTime(state.duration);
    this.elements.timeDisplay.textContent = `${current} / ${total}`;
  }

  private updateLyrics(lyric: string) {
    if (lyric === this.currentLyric || this.isTransitioning) return;
    
    this.currentLyric = lyric;
    this.isTransitioning = true;

    if (lyric.trim() === '') {
      // 隐藏歌词
      gsap.to(this.elements.lyricsDisplay, {
        opacity: 0,
        duration: 0.5,
        onComplete: () => {
          this.elements.lyricsDisplay.classList.remove('show');
          this.isTransitioning = false;
        }
      });
    } else {
      // 显示新歌词
      gsap.to(this.elements.lyricsDisplay, {
        opacity: 0,
        duration: 0.3,
        onComplete: () => {
          this.elements.lyricsDisplay.textContent = lyric;
          this.elements.lyricsDisplay.classList.add('show');
          
          gsap.to(this.elements.lyricsDisplay, {
            opacity: 1,
            duration: 0.5,
            onComplete: () => {
              this.isTransitioning = false;
            }
          });
        }
      });
    }
  }

  private hideLoading() {
    gsap.to(this.elements.loading, {
      opacity: 0,
      duration: 1,
      onComplete: () => {
        this.elements.loading.style.display = 'none';
      }
    });
  }

  private showEchoWall() {
    // 淡出3D场景控制
    gsap.to('#audio-controls', {
      opacity: 0,
      duration: 1
    });

    gsap.to('#lyrics-display', {
      opacity: 0,
      duration: 1
    });

    // 显示回响墙
    this.elements.echoWall.style.display = 'block';
    gsap.fromTo(this.elements.echoWall, 
      { opacity: 0 },
      { 
        opacity: 1, 
        duration: 2,
        delay: 1
      }
    );
  }

  private submitMessage() {
    const message = this.elements.userMessage.value.trim();
    if (!message) return;

    // 保存留言到本地存储
    this.saveMessage(message);

    // 清空输入框
    this.elements.userMessage.value = '';

    // 显示提交反馈
    this.showSubmitFeedback();
  }

  private saveMessage(message: string) {
    const messages = this.getStoredMessages();
    const newMessage = {
      id: Date.now().toString(),
      text: message,
      timestamp: Date.now(),
      position: {
        x: (Math.random() - 0.5) * 10,
        y: (Math.random() - 0.5) * 10,
        z: (Math.random() - 0.5) * 10
      }
    };

    messages.push(newMessage);
    
    // 限制最大留言数量
    if (messages.length > 100) {
      messages.shift();
    }

    localStorage.setItem('echoMessages', JSON.stringify(messages));
    
    // 触发自定义事件，通知回响墙更新
    window.dispatchEvent(new CustomEvent('newMessage', { detail: newMessage }));
  }

  private getStoredMessages() {
    const stored = localStorage.getItem('echoMessages');
    return stored ? JSON.parse(stored) : [];
  }

  private showSubmitFeedback() {
    const originalText = this.elements.submitMessage.textContent;
    this.elements.submitMessage.textContent = '已发送';
    this.elements.submitMessage.disabled = true;

    setTimeout(() => {
      this.elements.submitMessage.textContent = originalText;
      this.elements.submitMessage.disabled = false;
    }, 2000);
  }

  // 加载音频文件
  loadAudio(src: string) {
    this.elements.loading.style.display = 'flex';
    this.audioManager.loadAudio(src);
  }

  // 获取存储的留言（供回响墙使用）
  getMessages() {
    return this.getStoredMessages();
  }
}
