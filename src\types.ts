// 歌词时间轴数据结构
export interface LyricLine {
  time: number; // 秒
  text: string;
  emotion: EmotionType;
}

// 情感类型
export type EmotionType = 'mist' | 'cocoon' | 'bubble' | 'normal';

// 3D场景状态
export interface SceneState {
  mistIntensity: number;
  cocoonProgress: number;
  bubbleCount: number;
  cameraPosition: { x: number; y: number; z: number };
}

// 用户留言数据
export interface UserMessage {
  id: string;
  text: string;
  timestamp: number;
  position: { x: number; y: number; z: number };
}

// 音频播放状态
export interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
}

// 粒子系统配置
export interface ParticleConfig {
  count: number;
  size: number;
  speed: number;
  opacity: number;
  color: string;
}

// 动画配置
export interface AnimationConfig {
  duration: number;
  easing: string;
  delay: number;
}
