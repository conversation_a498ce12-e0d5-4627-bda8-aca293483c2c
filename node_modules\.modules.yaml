hoistPattern:
  - '*'
hoistedDependencies:
  '@esbuild/win32-x64@0.25.5':
    '@esbuild/win32-x64': private
  '@rollup/rollup-win32-x64-msvc@4.44.2':
    '@rollup/rollup-win32-x64-msvc': private
  '@types/estree@1.0.8':
    '@types/estree': private
  esbuild@0.25.5:
    esbuild: private
  fdir@6.4.6(picomatch@4.0.2):
    fdir: private
  nanoid@3.3.11:
    nanoid: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  rollup@4.44.2:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  tinyglobby@0.2.14:
    tinyglobby: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Mon, 07 Jul 2025 05:36:15 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.5'
  - '@esbuild/android-arm64@0.25.5'
  - '@esbuild/android-arm@0.25.5'
  - '@esbuild/android-x64@0.25.5'
  - '@esbuild/darwin-arm64@0.25.5'
  - '@esbuild/darwin-x64@0.25.5'
  - '@esbuild/freebsd-arm64@0.25.5'
  - '@esbuild/freebsd-x64@0.25.5'
  - '@esbuild/linux-arm64@0.25.5'
  - '@esbuild/linux-arm@0.25.5'
  - '@esbuild/linux-ia32@0.25.5'
  - '@esbuild/linux-loong64@0.25.5'
  - '@esbuild/linux-mips64el@0.25.5'
  - '@esbuild/linux-ppc64@0.25.5'
  - '@esbuild/linux-riscv64@0.25.5'
  - '@esbuild/linux-s390x@0.25.5'
  - '@esbuild/linux-x64@0.25.5'
  - '@esbuild/netbsd-arm64@0.25.5'
  - '@esbuild/netbsd-x64@0.25.5'
  - '@esbuild/openbsd-arm64@0.25.5'
  - '@esbuild/openbsd-x64@0.25.5'
  - '@esbuild/sunos-x64@0.25.5'
  - '@esbuild/win32-arm64@0.25.5'
  - '@esbuild/win32-ia32@0.25.5'
  - '@rollup/rollup-android-arm-eabi@4.44.2'
  - '@rollup/rollup-android-arm64@4.44.2'
  - '@rollup/rollup-darwin-arm64@4.44.2'
  - '@rollup/rollup-darwin-x64@4.44.2'
  - '@rollup/rollup-freebsd-arm64@4.44.2'
  - '@rollup/rollup-freebsd-x64@4.44.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.44.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.44.2'
  - '@rollup/rollup-linux-arm64-gnu@4.44.2'
  - '@rollup/rollup-linux-arm64-musl@4.44.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.44.2'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.44.2'
  - '@rollup/rollup-linux-riscv64-musl@4.44.2'
  - '@rollup/rollup-linux-s390x-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-gnu@4.44.2'
  - '@rollup/rollup-linux-x64-musl@4.44.2'
  - '@rollup/rollup-win32-arm64-msvc@4.44.2'
  - '@rollup/rollup-win32-ia32-msvc@4.44.2'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\桌面\AI编程\web应用\be-caught-in-one's-own-trap\node_modules\.pnpm
virtualStoreDirMaxLength: 60
