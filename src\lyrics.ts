import type { LyricLine } from './types';

// 歌词数据 - 作茧自缚
export const lyrics: LyricLine[] = [
  { time: 0, text: "", emotion: 'normal' },
  { time: 5, text: "游走于谎言之中构建起来的朦胧", emotion: 'mist' },
  { time: 12, text: "模糊了真实的轮廓", emotion: 'mist' },
  { time: 18, text: "是我作茧自缚", emotion: 'cocoon' },
  { time: 25, text: "将自己困在虚假的温柔", emotion: 'cocoon' },
  { time: 32, text: "相信你 多轻浮的吻", emotion: 'bubble' },
  { time: 38, text: "如泡沫般易碎的承诺", emotion: 'bubble' },
  { time: 45, text: "我在迷雾中寻找", emotion: 'mist' },
  { time: 52, text: "那个真实的自己", emotion: 'normal' },
  { time: 58, text: "却发现早已迷失", emotion: 'mist' },
  { time: 65, text: "在自己编织的茧中", emotion: 'cocoon' },
  { time: 72, text: "无法挣脱", emotion: 'cocoon' },
  { time: 78, text: "那些美丽的谎言", emotion: 'bubble' },
  { time: 85, text: "如气泡般破灭", emotion: 'bubble' },
  { time: 92, text: "留下的只有", emotion: 'normal' },
  { time: 98, text: "深深的孤独", emotion: 'mist' },
  { time: 105, text: "作茧自缚", emotion: 'cocoon' },
  { time: 112, text: "是我选择的宿命", emotion: 'cocoon' },
  { time: 120, text: "", emotion: 'normal' }
];

// 获取当前时间对应的歌词
export function getCurrentLyric(currentTime: number): LyricLine | null {
  for (let i = lyrics.length - 1; i >= 0; i--) {
    if (currentTime >= lyrics[i].time) {
      return lyrics[i];
    }
  }
  return null;
}

// 获取下一句歌词
export function getNextLyric(currentTime: number): LyricLine | null {
  for (let i = 0; i < lyrics.length; i++) {
    if (currentTime < lyrics[i].time) {
      return lyrics[i];
    }
  }
  return null;
}
