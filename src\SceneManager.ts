import * as THREE from 'three';
import type { SceneState, EmotionType } from './types';
import { MistEffect } from './effects/MistEffect';
import { CocoonEffect } from './effects/CocoonEffect';
import { BubbleEffect } from './effects/BubbleEffect';

export class SceneManager {
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private renderer!: THREE.WebGLRenderer;
  private canvas: HTMLCanvasElement;

  // 效果系统
  private mistEffect!: MistEffect;
  private cocoonEffect!: CocoonEffect;
  private bubbleEffect!: BubbleEffect;

  // 场景状态
  private state: SceneState;

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.state = {
      mistIntensity: 0,
      cocoonProgress: 0,
      bubbleCount: 0,
      cameraPosition: { x: 0, y: 0, z: 5 }
    };

    this.initScene();
    this.initEffects();
    this.setupEventListeners();
    this.animate();
  }

  private initScene() {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000000);
    this.scene.fog = new THREE.Fog(0x000000, 1, 100);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 0, 5);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: true
    });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    // 添加基础光照
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    this.scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(5, 5, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);

    // 添加点光源用于氛围
    const pointLight = new THREE.PointLight(0x667eea, 1, 100);
    pointLight.position.set(0, 0, 0);
    this.scene.add(pointLight);
  }

  private initEffects() {
    this.mistEffect = new MistEffect(this.scene);
    this.cocoonEffect = new CocoonEffect(this.scene);
    this.bubbleEffect = new BubbleEffect(this.scene);
  }

  private setupEventListeners() {
    window.addEventListener('resize', this.onWindowResize.bind(this));
    
    // 鼠标交互
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('click', this.onClick.bind(this));
  }

  private onWindowResize() {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }

  private onMouseMove(event: MouseEvent) {
    const mouse = new THREE.Vector2();
    mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
    
    // 根据鼠标位置调整相机
    this.camera.position.x += (mouse.x * 0.5 - this.camera.position.x) * 0.05;
    this.camera.position.y += (mouse.y * 0.5 - this.camera.position.y) * 0.05;
    this.camera.lookAt(0, 0, 0);
    
    // 迷雾交互 - 鼠标移动时暂时减少迷雾
    if (this.state.mistIntensity > 0) {
      this.mistEffect.addMouseInteraction(mouse.x, mouse.y);
    }
  }

  private onClick(event: MouseEvent) {
    // 气泡交互 - 点击时破坏气泡
    if (this.state.bubbleCount > 0) {
      const mouse = new THREE.Vector2();
      mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
      mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;
      
      this.bubbleEffect.popBubbleAt(mouse);
    }
  }

  // 根据情感类型更新场景
  updateEmotion(emotion: EmotionType, intensity: number = 1) {
    switch (emotion) {
      case 'mist':
        this.state.mistIntensity = intensity;
        this.mistEffect.setIntensity(intensity);
        break;
      case 'cocoon':
        this.state.cocoonProgress = intensity;
        this.cocoonEffect.setProgress(intensity);
        break;
      case 'bubble':
        this.state.bubbleCount = Math.floor(intensity * 20);
        this.bubbleEffect.setBubbleCount(this.state.bubbleCount);
        break;
      case 'normal':
        // 逐渐清除所有效果
        this.clearEffects();
        break;
    }
  }

  private clearEffects() {
    this.mistEffect.setIntensity(0);
    this.cocoonEffect.setProgress(0);
    this.bubbleEffect.setBubbleCount(0);
    
    this.state.mistIntensity = 0;
    this.state.cocoonProgress = 0;
    this.state.bubbleCount = 0;
  }

  private animate() {
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新效果
    this.mistEffect.update();
    this.cocoonEffect.update();
    this.bubbleEffect.update();
    
    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  // 获取场景状态
  getState(): SceneState {
    return { ...this.state };
  }

  // 销毁场景
  dispose() {
    this.mistEffect.dispose();
    this.cocoonEffect.dispose();
    this.bubbleEffect.dispose();
    this.renderer.dispose();
  }
}
