import * as THREE from 'three';
import { gsap } from 'gsap';

export class MistEffect {
  private scene: THREE.Scene;
  private particles!: THREE.Points;
  private geometry!: THREE.BufferGeometry;
  private material!: THREE.PointsMaterial;
  private positions!: Float32Array;
  private velocities!: Float32Array;
  private opacity!: Float32Array;
  private particleCount: number = 2000;
  private intensity: number = 0;
  private mouseInteraction: { x: number; y: number; strength: number } = { x: 0, y: 0, strength: 0 };

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.init();
  }

  private init() {
    // 创建粒子几何体
    this.geometry = new THREE.BufferGeometry();
    
    // 初始化位置、速度和透明度数组
    this.positions = new Float32Array(this.particleCount * 3);
    this.velocities = new Float32Array(this.particleCount * 3);
    this.opacity = new Float32Array(this.particleCount);

    // 随机分布粒子
    for (let i = 0; i < this.particleCount; i++) {
      const i3 = i * 3;
      
      // 位置
      this.positions[i3] = (Math.random() - 0.5) * 20;
      this.positions[i3 + 1] = (Math.random() - 0.5) * 20;
      this.positions[i3 + 2] = (Math.random() - 0.5) * 20;
      
      // 速度
      this.velocities[i3] = (Math.random() - 0.5) * 0.02;
      this.velocities[i3 + 1] = (Math.random() - 0.5) * 0.02;
      this.velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;
      
      // 透明度
      this.opacity[i] = Math.random() * 0.5;
    }

    this.geometry.setAttribute('position', new THREE.BufferAttribute(this.positions, 3));
    this.geometry.setAttribute('opacity', new THREE.BufferAttribute(this.opacity, 1));

    // 创建材质
    this.material = new THREE.PointsMaterial({
      color: 0x888888,
      size: 2,
      transparent: true,
      opacity: 0,
      vertexColors: false,
      blending: THREE.AdditiveBlending,
      depthWrite: false
    });

    // 创建粒子系统
    this.particles = new THREE.Points(this.geometry, this.material);
    this.scene.add(this.particles);
  }

  // 设置迷雾强度
  setIntensity(intensity: number) {
    this.intensity = Math.max(0, Math.min(1, intensity));
    
    // 使用GSAP平滑过渡
    gsap.to(this.material, {
      opacity: this.intensity * 0.6,
      duration: 2,
      ease: "power2.inOut"
    });

    // 更新粒子透明度
    for (let i = 0; i < this.particleCount; i++) {
      gsap.to(this.opacity, {
        [i]: Math.random() * this.intensity * 0.8,
        duration: Math.random() * 3 + 1,
        ease: "power2.inOut"
      });
    }
  }

  // 添加鼠标交互
  addMouseInteraction(mouseX: number, mouseY: number) {
    this.mouseInteraction.x = mouseX;
    this.mouseInteraction.y = mouseY;
    this.mouseInteraction.strength = 1;
    
    // 强度逐渐衰减
    gsap.to(this.mouseInteraction, {
      strength: 0,
      duration: 2,
      ease: "power2.out"
    });
  }

  // 更新粒子
  update() {
    if (this.intensity === 0) return;

    const positions = this.geometry.attributes.position.array as Float32Array;
    const opacities = this.geometry.attributes.opacity.array as Float32Array;

    for (let i = 0; i < this.particleCount; i++) {
      const i3 = i * 3;
      
      // 更新位置
      positions[i3] += this.velocities[i3];
      positions[i3 + 1] += this.velocities[i3 + 1];
      positions[i3 + 2] += this.velocities[i3 + 2];
      
      // 边界检查
      if (Math.abs(positions[i3]) > 10) {
        this.velocities[i3] *= -1;
      }
      if (Math.abs(positions[i3 + 1]) > 10) {
        this.velocities[i3 + 1] *= -1;
      }
      if (Math.abs(positions[i3 + 2]) > 10) {
        this.velocities[i3 + 2] *= -1;
      }

      // 鼠标交互效果
      if (this.mouseInteraction.strength > 0) {
        const dx = positions[i3] - this.mouseInteraction.x * 5;
        const dy = positions[i3 + 1] - this.mouseInteraction.y * 5;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 3) {
          const force = (3 - distance) / 3 * this.mouseInteraction.strength * 0.1;
          positions[i3] += dx * force;
          positions[i3 + 1] += dy * force;
          
          // 临时降低透明度
          opacities[i] = this.opacity[i] * (1 - this.mouseInteraction.strength * 0.5);
        } else {
          opacities[i] = this.opacity[i];
        }
      } else {
        opacities[i] = this.opacity[i];
      }
    }

    // 标记需要更新
    this.geometry.attributes.position.needsUpdate = true;
    this.geometry.attributes.opacity.needsUpdate = true;
  }

  // 销毁
  dispose() {
    this.scene.remove(this.particles);
    this.geometry.dispose();
    this.material.dispose();
  }
}
