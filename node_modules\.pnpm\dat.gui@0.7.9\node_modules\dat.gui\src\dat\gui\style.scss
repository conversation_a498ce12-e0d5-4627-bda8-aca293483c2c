$background-color: #1a1a1a;

$hover-lighten: 5%;
$border-lighten: 7%;
$active-lighten: 10%;

$number-color: #2FA1D6;
$boolean-color: #806787;
$string-color: #1ed36f;
$function-color: #e61d5f;
$save-row-color: #dad5cb;
$button-color: darken($save-row-color, 10%);
$border-color: lighten($background-color, $border-lighten);
$input-color: lighten($background-color, 8.5%);

@mixin transition($prop, $time, $curve) {
  -webkit-transition: $prop $time $curve;
  -o-transition: $prop $time $curve;
  -moz-transition: $prop $time $curve;
  transition: $prop $time $curve;
}

@mixin gradient($a, $b) {
  background: -webkit-gradient(linear, 0% 0%, 0% 100%, from($a), to($b));
  background: -o-gradient(linear, 0% 0%, 0% 100%, from($a), to($b));
  background: -moz-gradient(linear, 0% 0%, 0% 100%, from($a), to($b));
}

@mixin button() {
  margin-left: 5px;
  margin-top: 1px;
  border-radius: 2px;
  font-size: 9px;
  line-height: 7px;
  padding: 4px 4px 5px 4px;
  background: $button-color;
  color: #fff;
  text-shadow: 0 1px 0 darken($button-color, 10%);
  box-shadow: 0 -1px 0 darken($button-color, 10%);
  cursor: pointer;
}

@mixin gears() {
  background: $button-color url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAsAAAANCAYAAAB/9ZQ7AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAQJJREFUeNpiYKAU/P//PwGIC/ApCABiBSAW+I8AClAcgKxQ4T9hoMAEUrxx2QSGN6+egDX+/vWT4e7N82AMYoPAx/evwWoYoSYbACX2s7KxCxzcsezDh3evFoDEBYTEEqycggWAzA9AuUSQQgeYPa9fPv6/YWm/Acx5IPb7ty/fw+QZblw67vDs8R0YHyQhgObx+yAJkBqmG5dPPDh1aPOGR/eugW0G4vlIoTIfyFcA+QekhhHJhPdQxbiAIguMBTQZrPD7108M6roWYDFQiIAAv6Aow/1bFwXgis+f2LUAynwoIaNcz8XNx3Dl7MEJUDGQpx9gtQ8YCueB+D26OECAAQDadt7e46D42QAAAABJRU5ErkJggg==) 2px 1px no-repeat;
  height: 7px;
  width: 8px;
}

@import "structure";

/** Main type */
.dg {

  color: #eee;
  font: 11px 'Lucida Grande', sans-serif;
  text-shadow: 0 -1px 0 #111;

  /** Auto place */
  &.main {

    /** Scrollbar */
    &::-webkit-scrollbar {
      width: 5px;
      background: $background-color;
    }
    &::-webkit-scrollbar-corner {
      height: 0;
      display: none;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 5px;
      background: lighten($background-color, 30%);
    }

  }

  li {

    &:not(.folder) {
      background: $background-color;
      border-bottom: 1px solid $border-color;
    }

    &.save-row {

      line-height: 25px;
      background: $save-row-color;
      border: 0;

      select {
        margin-left: 5px;
        width: 108px;

      }

      .button {

        &.gears {
          @include gears;
        }

        @include button;

        &:hover {
          background-color: darken($button-color, 5%);
          box-shadow: 0 -1px 0 darken($button-color, 10%);
        }

      }

    }

    &.folder {
      border-bottom: 0;
    }

    &.title {
      padding-left: 16px;
      background: #000 url(data:image/gif;base64,R0lGODlhBQAFAJEAAP////Pz8////////yH5BAEAAAIALAAAAAAFAAUAAAIIlI+hKgFxoCgAOw==) 6px 10px no-repeat;
      cursor: pointer;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

  }

  .closed li.title {
    background-image: url(data:image/gif;base64,R0lGODlhBQAFAJEAAP////Pz8////////yH5BAEAAAIALAAAAAAFAAUAAAIIlGIWqMCbWAEAOw==);
  }

  /* Controller row, <li> */
  .cr {

    &.boolean {
      border-left: 3px solid $boolean-color;
    }

    &.color {
      border-left: 3px solid;
    }

    &.function {
      border-left: 3px solid $function-color;
    }

    &.number {
      border-left: 3px solid $number-color;
      input[type=text] {
        color: $number-color;
      }
    }

    &.string {
      border-left: 3px solid $string-color;
      input[type=text] {
        color: $string-color;
      }
    }

    &.function:hover,
    &.boolean:hover {
      background: #111;
    }

  }

  /** Controllers */
  .c {

    input[type=text] {

      background: $input-color;
      outline: none;
      &:hover {
        background: lighten($input-color, $hover-lighten);
      }
      &:focus {
        background: lighten($input-color, $active-lighten);
        color: #fff;
      }

    }

    .slider {
      background: $input-color;
      cursor: ew-resize;
    }

    .slider-fg {
      background: $number-color;
      max-width: 100%;
    }

    .slider:hover {
      background: lighten($input-color, $hover-lighten);
      .slider-fg {
        background: lighten($number-color, $hover-lighten);
      }
    }

  }

}
