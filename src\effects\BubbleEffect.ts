import * as THREE from 'three';
import { gsap } from 'gsap';

interface Bubble {
  mesh: THREE.Mesh;
  velocity: THREE.Vector3;
  life: number;
  maxLife: number;
  size: number;
}

export class BubbleEffect {
  private scene: THREE.Scene;
  private bubbles: Bubble[] = [];
  private bubbleCount: number = 0;
  private maxBubbles: number = 30;
  private bubbleGeometry!: THREE.SphereGeometry;
  private bubbleMaterial!: THREE.MeshPhongMaterial;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.init();
  }

  private init() {
    // 创建共享的气泡几何体和材质
    this.bubbleGeometry = new THREE.SphereGeometry(0.1, 16, 12);
    this.bubbleMaterial = new THREE.MeshPhongMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.3,
      shininess: 100,
      specular: 0x222222
    });
  }

  // 设置气泡数量
  setBubbleCount(count: number) {
    this.bubbleCount = Math.max(0, Math.min(this.maxBubbles, count));
    
    if (this.bubbleCount === 0) {
      this.clearAllBubbles();
      return;
    }

    // 添加新气泡
    while (this.bubbles.length < this.bubbleCount) {
      this.createBubble();
    }

    // 移除多余气泡
    while (this.bubbles.length > this.bubbleCount) {
      this.removeBubble(this.bubbles.length - 1);
    }
  }

  private createBubble() {
    const size = Math.random() * 0.3 + 0.1;
    const geometry = new THREE.SphereGeometry(size, 16, 12);
    
    // 创建彩虹色泽材质
    const hue = Math.random();
    const material = new THREE.MeshPhongMaterial({
      color: new THREE.Color().setHSL(hue, 0.7, 0.8),
      transparent: true,
      opacity: 0.4,
      shininess: 100,
      specular: 0x444444
    });

    const mesh = new THREE.Mesh(geometry, material);
    
    // 随机位置
    mesh.position.set(
      (Math.random() - 0.5) * 10,
      (Math.random() - 0.5) * 10,
      (Math.random() - 0.5) * 10
    );

    const bubble: Bubble = {
      mesh,
      velocity: new THREE.Vector3(
        (Math.random() - 0.5) * 0.02,
        Math.random() * 0.03 + 0.01, // 向上飘
        (Math.random() - 0.5) * 0.02
      ),
      life: 0,
      maxLife: Math.random() * 10 + 5, // 5-15秒生命周期
      size
    };

    this.bubbles.push(bubble);
    this.scene.add(mesh);

    // 入场动画
    mesh.scale.setScalar(0);
    gsap.to(mesh.scale, {
      x: 1,
      y: 1,
      z: 1,
      duration: 1,
      ease: "elastic.out(1, 0.3)"
    });
  }

  private removeBubble(index: number) {
    if (index < 0 || index >= this.bubbles.length) return;

    const bubble = this.bubbles[index];
    
    // 退场动画
    gsap.to(bubble.mesh.scale, {
      x: 0,
      y: 0,
      z: 0,
      duration: 0.5,
      ease: "power2.in",
      onComplete: () => {
        this.scene.remove(bubble.mesh);
        bubble.mesh.geometry.dispose();
        (bubble.mesh.material as THREE.Material).dispose();
      }
    });

    this.bubbles.splice(index, 1);
  }

  private clearAllBubbles() {
    this.bubbles.forEach((_, index) => {
      setTimeout(() => {
        this.removeBubble(0); // 总是移除第一个，因为数组在缩短
      }, index * 100); // 错开移除时间
    });
  }

  // 在指定位置破坏气泡
  popBubbleAt(mousePosition: THREE.Vector2) {
    const raycaster = new THREE.Raycaster();
    const camera = new THREE.PerspectiveCamera(); // 临时相机，实际应该传入真实相机
    
    raycaster.setFromCamera(mousePosition, camera);
    
    for (let i = this.bubbles.length - 1; i >= 0; i--) {
      const bubble = this.bubbles[i];
      const intersects = raycaster.intersectObject(bubble.mesh);
      
      if (intersects.length > 0) {
        this.popBubble(i);
        break; // 只破坏一个气泡
      }
    }
  }

  private popBubble(index: number) {
    if (index < 0 || index >= this.bubbles.length) return;

    const bubble = this.bubbles[index];
    
    // 破裂效果
    gsap.to(bubble.mesh.material, {
      opacity: 0,
      duration: 0.3,
      ease: "power2.out"
    });

    gsap.to(bubble.mesh.scale, {
      x: 2,
      y: 2,
      z: 2,
      duration: 0.3,
      ease: "power2.out",
      onComplete: () => {
        this.scene.remove(bubble.mesh);
        bubble.mesh.geometry.dispose();
        (bubble.mesh.material as THREE.Material).dispose();
      }
    });

    this.bubbles.splice(index, 1);
    
    // 创建新气泡补充
    if (this.bubbles.length < this.bubbleCount) {
      setTimeout(() => {
        this.createBubble();
      }, 1000);
    }
  }

  // 更新气泡
  update() {
    for (let i = this.bubbles.length - 1; i >= 0; i--) {
      const bubble = this.bubbles[i];
      
      // 更新位置
      bubble.mesh.position.add(bubble.velocity);
      
      // 更新生命周期
      bubble.life += 0.016; // 假设60fps
      
      // 飘动效果
      const time = Date.now() * 0.001;
      bubble.mesh.position.x += Math.sin(time + i) * 0.001;
      bubble.mesh.position.z += Math.cos(time + i) * 0.001;
      
      // 旋转
      bubble.mesh.rotation.x += 0.01;
      bubble.mesh.rotation.y += 0.015;
      
      // 边界检查
      if (bubble.mesh.position.y > 10) {
        bubble.mesh.position.y = -10;
      }
      if (Math.abs(bubble.mesh.position.x) > 8) {
        bubble.velocity.x *= -1;
      }
      if (Math.abs(bubble.mesh.position.z) > 8) {
        bubble.velocity.z *= -1;
      }
      
      // 生命周期结束
      if (bubble.life >= bubble.maxLife) {
        this.popBubble(i);
      }
      
      // 透明度变化
      const lifeRatio = bubble.life / bubble.maxLife;
      const opacity = 0.4 * (1 - lifeRatio * 0.5);
      (bubble.mesh.material as THREE.MeshPhongMaterial).opacity = opacity;
    }
  }

  // 销毁
  dispose() {
    this.clearAllBubbles();
    this.bubbleGeometry.dispose();
    this.bubbleMaterial.dispose();
  }
}
