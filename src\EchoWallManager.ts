import * as THREE from 'three';
import { gsap } from 'gsap';
import type { UserMessage } from './types';

interface FloatingText {
  mesh: THREE.Mesh;
  message: UserMessage;
  velocity: THREE.Vector3;
  life: number;
}

export class EchoWallManager {
  private scene!: THREE.Scene;
  private camera!: THREE.PerspectiveCamera;
  private renderer!: THREE.WebGLRenderer;
  private canvas: HTMLCanvasElement;
  private floatingTexts: FloatingText[] = [];
  private lightPoints!: THREE.Points;

  constructor() {
    this.canvas = document.getElementById('echo-canvas') as HTMLCanvasElement;
    this.initScene();
    this.setupEventListeners();
    this.animate();
  }

  private initScene() {
    // 创建场景
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x000011);

    // 创建相机
    this.camera = new THREE.PerspectiveCamera(
      75,
      this.canvas.clientWidth / this.canvas.clientHeight,
      0.1,
      1000
    );
    this.camera.position.set(0, 0, 10);

    // 创建渲染器
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true
    });
    this.renderer.setSize(this.canvas.clientWidth, this.canvas.clientHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    // 创建背景星空
    this.createStarField();

    // 创建文字材质
    this.textMaterial = new THREE.MeshBasicMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0.8
    });

    // 加载现有留言
    this.loadExistingMessages();
  }

  private createStarField() {
    const starCount = 1000;
    const positions = new Float32Array(starCount * 3);
    const colors = new Float32Array(starCount * 3);

    for (let i = 0; i < starCount; i++) {
      const i3 = i * 3;
      
      // 位置
      positions[i3] = (Math.random() - 0.5) * 100;
      positions[i3 + 1] = (Math.random() - 0.5) * 100;
      positions[i3 + 2] = (Math.random() - 0.5) * 100;
      
      // 颜色
      const color = new THREE.Color().setHSL(Math.random(), 0.5, 0.8);
      colors[i3] = color.r;
      colors[i3 + 1] = color.g;
      colors[i3 + 2] = color.b;
    }

    const geometry = new THREE.BufferGeometry();
    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

    const material = new THREE.PointsMaterial({
      size: 2,
      transparent: true,
      opacity: 0.6,
      vertexColors: true,
      blending: THREE.AdditiveBlending
    });

    this.lightPoints = new THREE.Points(geometry, material);
    this.scene.add(this.lightPoints);
  }

  private createTextMesh(message: UserMessage): THREE.Mesh {
    // 创建文字几何体（简化版本，实际项目中可能需要使用TextGeometry或Canvas纹理）
    const geometry = new THREE.PlaneGeometry(2, 0.5);
    
    // 创建Canvas纹理来渲染文字
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    canvas.width = 512;
    canvas.height = 128;
    
    // 设置文字样式
    context.fillStyle = 'rgba(255, 255, 255, 0.9)';
    context.font = '24px Microsoft YaHei';
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    
    // 绘制文字
    const maxWidth = 480;
    const words = message.text.split('');
    let line = '';
    let y = 64;
    
    for (let i = 0; i < words.length; i++) {
      const testLine = line + words[i];
      const metrics = context.measureText(testLine);
      
      if (metrics.width > maxWidth && i > 0) {
        context.fillText(line, 256, y);
        line = words[i];
        y += 30;
      } else {
        line = testLine;
      }
    }
    context.fillText(line, 256, y);
    
    // 创建纹理
    const texture = new THREE.CanvasTexture(canvas);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true,
      opacity: 0.8
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.copy(message.position);
    
    return mesh;
  }

  private loadExistingMessages() {
    const stored = localStorage.getItem('echoMessages');
    if (!stored) return;

    const messages: UserMessage[] = JSON.parse(stored);
    messages.forEach(message => {
      this.addFloatingText(message);
    });
  }

  private addFloatingText(message: UserMessage) {
    const mesh = this.createTextMesh(message);
    
    const floatingText: FloatingText = {
      mesh,
      message,
      velocity: new THREE.Vector3(
        (Math.random() - 0.5) * 0.01,
        (Math.random() - 0.5) * 0.01,
        (Math.random() - 0.5) * 0.01
      ),
      life: 0
    };

    this.floatingTexts.push(floatingText);
    this.scene.add(mesh);

    // 入场动画
    mesh.scale.setScalar(0);
    gsap.to(mesh.scale, {
      x: 1,
      y: 1,
      z: 1,
      duration: 2,
      ease: "elastic.out(1, 0.3)"
    });
  }

  private setupEventListeners() {
    // 监听新留言事件
    window.addEventListener('newMessage', ((event: CustomEvent) => {
      this.addFloatingText(event.detail);
    }) as EventListener);

    // 窗口大小变化
    window.addEventListener('resize', () => {
      this.onWindowResize();
    });

    // 鼠标移动交互
    this.canvas.addEventListener('mousemove', (event) => {
      this.onMouseMove(event);
    });
  }

  private onWindowResize() {
    const width = this.canvas.clientWidth;
    const height = this.canvas.clientHeight;
    
    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  private onMouseMove(event: MouseEvent) {
    const mouse = new THREE.Vector2();
    mouse.x = (event.clientX / this.canvas.clientWidth) * 2 - 1;
    mouse.y = -(event.clientY / this.canvas.clientHeight) * 2 + 1;
    
    // 相机跟随鼠标轻微移动
    this.camera.position.x += (mouse.x * 2 - this.camera.position.x) * 0.05;
    this.camera.position.y += (mouse.y * 2 - this.camera.position.y) * 0.05;
    this.camera.lookAt(0, 0, 0);
  }

  private animate() {
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新漂浮文字
    this.floatingTexts.forEach((floatingText) => {
      const { mesh, velocity } = floatingText;
      
      // 更新位置
      mesh.position.add(velocity);
      
      // 边界检查
      if (Math.abs(mesh.position.x) > 15) {
        velocity.x *= -1;
      }
      if (Math.abs(mesh.position.y) > 10) {
        velocity.y *= -1;
      }
      if (Math.abs(mesh.position.z) > 15) {
        velocity.z *= -1;
      }
      
      // 旋转
      mesh.rotation.y += 0.005;
      
      // 生命周期
      floatingText.life += 0.016;
      
      // 透明度变化
      const opacity = 0.8 * Math.sin(floatingText.life * 0.5) * 0.3 + 0.7;
      (mesh.material as THREE.MeshBasicMaterial).opacity = opacity;
    });

    // 旋转星空
    if (this.lightPoints) {
      this.lightPoints.rotation.y += 0.001;
      this.lightPoints.rotation.x += 0.0005;
    }

    // 渲染场景
    this.renderer.render(this.scene, this.camera);
  }

  // 销毁
  dispose() {
    this.floatingTexts.forEach(floatingText => {
      this.scene.remove(floatingText.mesh);
      floatingText.mesh.geometry.dispose();
      (floatingText.mesh.material as THREE.Material).dispose();
    });
    
    this.renderer.dispose();
  }
}
