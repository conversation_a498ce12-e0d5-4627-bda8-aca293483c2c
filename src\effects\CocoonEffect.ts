import * as THREE from 'three';
import { gsap } from 'gsap';

export class CocoonEffect {
  private scene: THREE.Scene;
  private cocoonGroup!: THREE.Group;
  private silkLines: THREE.Line[] = [];
  private cocoonMesh: THREE.Mesh | null = null;
  private progress: number = 0;
  private lineCount: number = 50;
  private radius: number = 3;

  constructor(scene: THREE.Scene) {
    this.scene = scene;
    this.init();
  }

  private init() {
    this.cocoonGroup = new THREE.Group();
    this.scene.add(this.cocoonGroup);
    
    this.createSilkLines();
    this.createCocoonShell();
  }

  private createSilkLines() {
    // 创建丝线效果
    for (let i = 0; i < this.lineCount; i++) {
      const points: THREE.Vector3[] = [];
      const segments = 20;
      
      // 创建螺旋形丝线路径
      for (let j = 0; j <= segments; j++) {
        const t = j / segments;
        const angle = t * Math.PI * 4 + (i / this.lineCount) * Math.PI * 2;
        const r = this.radius * (1 - t * 0.3);
        const y = (t - 0.5) * 4;
        
        points.push(new THREE.Vector3(
          Math.cos(angle) * r,
          y,
          Math.sin(angle) * r
        ));
      }

      const geometry = new THREE.BufferGeometry().setFromPoints(points);
      const material = new THREE.LineBasicMaterial({
        color: 0xffffff,
        transparent: true,
        opacity: 0,
        linewidth: 2
      });

      const line = new THREE.Line(geometry, material);
      this.silkLines.push(line);
      this.cocoonGroup.add(line);
    }
  }

  private createCocoonShell() {
    // 创建茧的外壳
    const geometry = new THREE.SphereGeometry(this.radius, 32, 16);
    const material = new THREE.MeshPhongMaterial({
      color: 0xffffff,
      transparent: true,
      opacity: 0,
      side: THREE.DoubleSide,
      wireframe: false
    });

    this.cocoonMesh = new THREE.Mesh(geometry, material);
    this.cocoonGroup.add(this.cocoonMesh);
  }

  // 设置茧的生成进度
  setProgress(progress: number) {
    this.progress = Math.max(0, Math.min(1, progress));
    
    if (this.progress === 0) {
      this.hideCocoon();
      return;
    }

    this.animateCocoonGrowth();
  }

  private hideCocoon() {
    // 隐藏所有丝线
    this.silkLines.forEach((line, index) => {
      gsap.to(line.material, {
        opacity: 0,
        duration: 1,
        delay: index * 0.05,
        ease: "power2.out"
      });
    });

    // 隐藏茧壳
    if (this.cocoonMesh) {
      gsap.to(this.cocoonMesh.material, {
        opacity: 0,
        duration: 2,
        ease: "power2.out"
      });
    }
  }

  private animateCocoonGrowth() {
    // 逐渐显示丝线
    this.silkLines.forEach((line, index) => {
      const delay = (index / this.lineCount) * 3 * this.progress;
      
      gsap.to(line.material, {
        opacity: 0.6 * this.progress,
        duration: 2,
        delay: delay,
        ease: "power2.inOut"
      });

      // 丝线生长动画
      const originalGeometry = line.geometry;
      const positions = originalGeometry.attributes.position.array as Float32Array;
      const animatedPositions = new Float32Array(positions.length);
      
      gsap.to({ progress: 0 }, {
        progress: this.progress,
        duration: 3,
        delay: delay,
        ease: "power2.inOut",
        onUpdate: function() {
          const currentProgress = this.targets()[0].progress;
          const visibleVertices = Math.floor(currentProgress * positions.length / 3) * 3;
          
          for (let i = 0; i < visibleVertices; i++) {
            animatedPositions[i] = positions[i];
          }
          
          line.geometry.setAttribute('position', new THREE.BufferAttribute(animatedPositions, 3));
          line.geometry.attributes.position.needsUpdate = true;
        }
      });
    });

    // 茧壳逐渐形成
    if (this.cocoonMesh && this.progress > 0.5) {
      const shellOpacity = (this.progress - 0.5) * 0.4; // 最大透明度0.2
      
      gsap.to(this.cocoonMesh.material, {
        opacity: shellOpacity,
        duration: 3,
        ease: "power2.inOut"
      });

      // 茧壳缩放动画
      gsap.to(this.cocoonMesh.scale, {
        x: this.progress,
        y: this.progress,
        z: this.progress,
        duration: 3,
        ease: "elastic.out(1, 0.3)"
      });
    }
  }

  // 更新动画
  update() {
    if (this.progress === 0) return;

    // 旋转整个茧
    this.cocoonGroup.rotation.y += 0.005;
    
    // 丝线微动效果
    this.silkLines.forEach((line, index) => {
      const time = Date.now() * 0.001;
      line.rotation.z = Math.sin(time + index) * 0.1;
    });

    // 茧壳呼吸效果
    if (this.cocoonMesh && this.progress > 0.5) {
      const time = Date.now() * 0.002;
      const breathe = 1 + Math.sin(time) * 0.05;
      this.cocoonMesh.scale.setScalar(this.progress * breathe);
    }
  }

  // 销毁
  dispose() {
    this.silkLines.forEach(line => {
      line.geometry.dispose();
      (line.material as THREE.Material).dispose();
    });
    
    if (this.cocoonMesh) {
      this.cocoonMesh.geometry.dispose();
      (this.cocoonMesh.material as THREE.Material).dispose();
    }
    
    this.scene.remove(this.cocoonGroup);
  }
}
